DESCRIPTION="i2R Network Manager"
HOMEPAGE="https://placide.enedis.fr/dev/nexus/ccma/i2r/i2r-network"
LICENSE="CLOSED"
LIC_FILES_CHKSUM=""

PV = "1.0.0"
PACKAGES = "${PN}"

inherit cargo cargo-update-recipe-vendored-crates ptest-cargo pkgconfig systemd

PACKAGECONFIG ??= "${@bb.utils.filter('DISTRO_FEATURES', 'systemd', d)}"
PACKAGECONFIG[systemd] = "--with-systemd,--without-systemd,systemd"

# Not supposed to be needed
INSANE_SKIP:${PN} += "already-stripped"

DEPENDS:append = " \
    dbus \
"

RDEPENDS:${PN} = "\
    embedded-hal \
    ppp \
"

# NOTE: I REALLY dislike using tokens here, they are going to be displayed in the logs ...
SRC_URI = "git://placide.enedis.fr/dev/nexus/ccma/i2r/i2r-network.git;protocol=https;user=${PLACIDE_USER}:${PLACIDE_TOKEN};branch=${NETWORK_GIT_BRANCH}"
SRCREV = "${AUTOREV}"

SRC_URI += " \
    file://systemd/i2r-network.service \
    file://ppp/chatscript/bg95 \
    file://ppp/peers/bg95 \
"

include ${BPN}-crates.inc

S = "${WORKDIR}/git"

do_install:append() {
    # Install the systemd service file
    install -d ${D}${systemd_system_unitdir}
    install -m 0644 ${WORKDIR}/systemd/i2r-network.service ${D}${systemd_system_unitdir}/

    install -d ${D}/etc/chatscripts/
    install -m 0644 ${WORKDIR}/ppp/chatscript/bg95 ${D}/etc/chatscripts/

    install -d ${D}/etc/ppp/peers/
    install -m 0644 ${WORKDIR}/ppp/peers/bg95 ${D}/etc/ppp/peers/

    # Remove temporary rustlib files (used at build time)
    rm -rf ${D}/usr/lib/rustlib
}

SYSTEMD_PACKAGES = "${@bb.utils.contains('DISTRO_FEATURES','systemd','${PN}','',d)}"
SYSTEMD_SERVICE:${PN} += "${@bb.utils.contains('PACKAGECONFIG', 'systemd', 'i2r-network.service', '', d)}"

FILES:${PN} += " \
    ${systemd_system_unitdir}/i2r-network.service \
    /etc/chatscripts/bg95 \
    /etc/ppp/peers/bg95 \
"
