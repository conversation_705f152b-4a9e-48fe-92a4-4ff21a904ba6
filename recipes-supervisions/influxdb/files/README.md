# InfluxDB Configuration for i2R

This recipe adds InfluxDB v2.7.10 to the i2R supervision stack.

## Features

- InfluxDB time series database
- Web UI accessible via nginx reverse proxy
- Integration with Telegraf for metrics collection
- Automatic initialization script

## Services

- **influxdb.service**: Main InfluxDB daemon
- **nginx**: Reverse proxy for web access

## Configuration

### InfluxDB
- Configuration file: `/etc/influxdb/influxdb.conf`
- Data directory: `/var/lib/influxdb/`
- Log directory: `/var/log/influxdb/`
- HTTP API: `http://localhost:8086`

### Web Access
- InfluxDB UI is accessible through nginx on port 8086
- nginx configuration: `/etc/nginx/sites-available/influxdb.conf`

### Telegraf Integration
Telegraf is configured to output metrics to both:
- Graphite (existing): `localhost:2003`
- InfluxDB: `http://localhost:8086`

## Initial Setup

After the first boot, run the initialization script:

```bash
sudo /usr/bin/influxdb-init.sh
```

This will:
1. Create the initial organization "i2r"
2. Create the "telegraf" bucket
3. Create an admin user "i2r-admin"
4. Generate an authentication token
5. **Automatically configure Telegraf with the token**
6. **Restart Telegraf to apply the configuration**

## Default Credentials

- Organization: `i2r`
- Bucket: `telegraf`
- Username: `i2r-admin`
- Password: `i2r-password`

**Note**: Change the default password in production!

## Telegraf Configuration

The Telegraf configuration is **automatically updated** by the initialization script. The token is stored in `/etc/default/telegraf` as an environment variable:

```bash
# View the current token
cat /etc/default/telegraf
```

The Telegraf configuration uses this environment variable:

```toml
[[outputs.influxdb_v2]]
  urls = ["http://localhost:8086"]
  token = "$INFLUXDB_TOKEN"  # Automatically populated
  organization = "i2r"
  bucket = "telegraf"
```

## Accessing the Web UI

1. Open a web browser
2. Navigate to `http://your-device-ip:8086`
3. Login with the credentials above
4. Explore your metrics in the Data Explorer

## Manual Token Management

If you need to manually update the InfluxDB token:

1. Generate a new token in the InfluxDB UI or CLI
2. Update the environment file:
   ```bash
   sudo nano /etc/default/telegraf
   # Update the INFLUXDB_TOKEN line
   ```
3. Restart Telegraf:
   ```bash
   sudo systemctl restart telegraf
   ```

## Troubleshooting

- Check service status: `systemctl status influxdb`
- View logs: `journalctl -u influxdb -f`
- Test API: `curl http://localhost:8086/health`
- Check Telegraf token: `cat /etc/default/telegraf`
- Verify Telegraf is sending data: `journalctl -u telegraf -f`
