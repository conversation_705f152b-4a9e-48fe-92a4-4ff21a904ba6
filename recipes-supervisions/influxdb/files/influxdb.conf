# InfluxDB v2 configuration for i2R

# HTTP bind address
http-bind-address = ":8086"

# Directory for storing InfluxDB data
engine-path = "/var/lib/influxdb/engine"
bolt-path = "/var/lib/influxdb/influxd.bolt"

# Log level
log-level = "info"

# Disable authentication for initial setup
# This will be configured during first run
no-tasks = false

# Storage configuration
storage-cache-max-memory-size = "1g"
storage-cache-snapshot-memory-size = "25m"
storage-cache-snapshot-write-cold-duration = "10m"
storage-compact-full-write-cold-duration = "4h"
storage-compact-throughput-burst = "48m"
storage-max-concurrent-compactions = 2
storage-max-index-log-file-size = "1m"
storage-retention-check-interval = "30m"
storage-series-id-set-cache-size = 100

# Query configuration
query-concurrency = 1024
query-initial-memory-bytes = 0
query-max-memory-bytes = 0
query-memory-bytes = 9223372036854775807
query-queue-size = 1024

# Flux configuration
flux-log-enabled = false

# Reporting configuration
reporting-disabled = true

# Session configuration
session-length = 60
session-renew-disabled = false
