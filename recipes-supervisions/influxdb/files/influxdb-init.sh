#!/bin/bash

# InfluxDB initialization script for i2R
# This script sets up the initial organization, bucket, and user
# It also configures Telegraf with the generated token

INFLUX_HOST="http://localhost:8086"
ORG_NAME="i2r"
BUCKET_NAME="telegraf"
USERNAME="i2r-admin"
PASSWORD="i2r-password"
RETENTION="30d"
TELEGRAF_ENV_FILE="/etc/default/telegraf"

# Wait for InfluxDB to be ready
echo "Waiting for InfluxDB to be ready..."
for i in {1..30}; do
    if curl -s "${INFLUX_HOST}/health" > /dev/null 2>&1; then
        echo "InfluxDB is ready!"
        break
    fi
    echo "Waiting for InfluxDB... (attempt $i/30)"
    sleep 2
done

# Check if InfluxDB is already initialized
if curl -s "${INFLUX_HOST}/api/v2/setup" | grep -q '"allowed":false'; then
    echo "InfluxDB is already initialized"
    exit 0
fi

# Initialize InfluxDB
echo "Initializing InfluxDB..."
SETUP_RESPONSE=$(curl -s -X POST "${INFLUX_HOST}/api/v2/setup" \
    -H "Content-Type: application/json" \
    -d "{
        \"username\": \"${USERNAME}\",
        \"password\": \"${PASSWORD}\",
        \"org\": \"${ORG_NAME}\",
        \"bucket\": \"${BUCKET_NAME}\",
        \"retentionPeriodSeconds\": 2592000
    }")

if echo "$SETUP_RESPONSE" | grep -q '"id"'; then
    echo "InfluxDB initialized successfully!"

    # Extract the token from the response
    TOKEN=$(echo "$SETUP_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

    if [ -n "$TOKEN" ]; then
        echo "Generated token: $TOKEN"
        echo "Organization: $ORG_NAME"
        echo "Bucket: $BUCKET_NAME"
        echo "Username: $USERNAME"
        echo ""

        # Update Telegraf environment file with the token
        echo "Updating Telegraf configuration..."

        # Create or update the Telegraf environment file
        if [ -f "$TELEGRAF_ENV_FILE" ]; then
            # Remove existing INFLUXDB_TOKEN line if it exists
            sed -i '/^INFLUXDB_TOKEN=/d' "$TELEGRAF_ENV_FILE"
        else
            # Create the directory if it doesn't exist
            mkdir -p "$(dirname "$TELEGRAF_ENV_FILE")"
            # Create the file with proper permissions
            touch "$TELEGRAF_ENV_FILE"
            chown i2r-system:i2r-system "$TELEGRAF_ENV_FILE"
            chmod 640 "$TELEGRAF_ENV_FILE"
        fi

        # Add the token to the environment file
        echo "INFLUXDB_TOKEN=$TOKEN" >> "$TELEGRAF_ENV_FILE"

        # Restart Telegraf to pick up the new token
        if systemctl is-active --quiet telegraf; then
            echo "Restarting Telegraf to apply new token..."
            systemctl restart telegraf
            if systemctl is-active --quiet telegraf; then
                echo "Telegraf restarted successfully!"
            else
                echo "Warning: Telegraf failed to restart. Check logs with: journalctl -u telegraf"
            fi
        else
            echo "Telegraf is not running. Start it with: systemctl start telegraf"
        fi

        echo ""
        echo "Configuration complete!"
        echo "- InfluxDB is accessible at: http://localhost:8086"
        echo "- Telegraf is configured to send metrics to InfluxDB"
        echo "- Token has been automatically configured in $TELEGRAF_ENV_FILE"
    fi
else
    echo "Failed to initialize InfluxDB"
    echo "Response: $SETUP_RESPONSE"
    exit 1
fi
