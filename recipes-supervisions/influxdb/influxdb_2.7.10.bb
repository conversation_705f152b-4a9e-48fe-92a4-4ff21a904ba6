DESCRIPTION = "InfluxDB is an open source time series database"
HOMEPAGE = "https://github.com/influxdata/influxdb"
LICENSE = "MIT"
SECTION = "database"

LIC_FILES_CHKSUM = "file://LICENSE;md5=ba8146ad9b14aba2b2d3c8c6e4c4f0e3"

SRCREV = "v2.7.10"
SRC_URI = "git://github.com/influxdata/influxdb.git;protocol=https;branch=v2.7 \
           file://influxdb.conf \
           file://service/influxdb.service \
           file://nginx-conf/influxdb.conf \
           file://influxdb-init.sh \
           file://README.md \
          "

S = "${WORKDIR}/git"

inherit goarch
inherit go
inherit systemd

EXTRA_OEMAKE = "GO='${GO}'"

INFLUXDB_OUT = "${WORKDIR}/build/out"

CGO_ENABLED ?= "1"
CGO_ENABLED:x86-64 = "0"

STATIC_FLAGS ?= ""
STATIC_FLAGS:x86-64 = "-a -pkgdir dontusecurrentpkgs"

# Define artifactory golang proxy
GOPROXY = "https://artifactory-zci.enedis.fr/artifactory/remote-go-proxy-golang"

do_compile[network] = "1"

do_compile() {
    export GOROOT="${STAGING_LIBDIR_NATIVE}/${TARGET_SYS}/go"
    export GOPATH="${S}"

    # Pass the needed cflags/ldflags so that cgo
    # can find the needed headers files and libraries
    export CFLAGS=""
    export LDFLAGS=""
    export CGO_CFLAGS="${BUILDSDK_CFLAGS} --sysroot=${STAGING_DIR_TARGET}"
    export CGO_LDFLAGS="${BUILDSDK_LDFLAGS} --sysroot=${STAGING_DIR_TARGET}"

    export STATIC_FLAGS="${STATIC_FLAGS}"
    export CGO_ENABLED="${CGO_ENABLED}"

    export GOARM="7"
    export DESTDIR="${INFLUXDB_OUT}"

    cd ${S}
    
    # Build InfluxDB
    ${GO} build -o ${INFLUXDB_OUT}/usr/bin/influxd ./cmd/influxd
    ${GO} build -o ${INFLUXDB_OUT}/usr/bin/influx ./cmd/influx

    # Golang forces permissions to 0500 on directories and 0400 on files in
    # the module cache which prevents us from easily cleaning up the build
    # directory. Let's just fix the permissions here so we don't have to
    # hack the clean tasks.
    chmod -R u+w ${S}/go/pkg/mod || true
}

do_install() {
    # /etc
    install -d ${D}${sysconfdir}/influxdb
    install -m 0644 ${WORKDIR}/influxdb.conf ${D}${sysconfdir}/influxdb/

    # /usr/bin
    install -d ${D}${bindir}
    install -m 0755 ${INFLUXDB_OUT}/usr/bin/influxd ${D}${bindir}/
    install -m 0755 ${INFLUXDB_OUT}/usr/bin/influx ${D}${bindir}/
    install -m 0755 ${WORKDIR}/influxdb-init.sh ${D}${bindir}/

    # /var/lib/influxdb for data storage
    install -d ${D}/var/lib/influxdb
    install -d ${D}/var/lib/influxdb/engine
    install -d ${D}/var/lib/influxdb/wal

    # /var/log/influxdb for logs
    install -d ${D}/var/log/influxdb

    # Install systemd service
    if ${@bb.utils.contains('DISTRO_FEATURES', 'systemd', 'true', 'false', d)}; then
       install -d ${D}${systemd_system_unitdir}
       install -m 0600 ${WORKDIR}/service/influxdb.service ${D}${systemd_system_unitdir}/
    fi

    # Create nginx folder to store the configuration file
    install -d ${D}${sysconfdir}/nginx/sites-available
    install -m 0644 ${WORKDIR}/nginx-conf/influxdb.conf ${D}${sysconfdir}/nginx/sites-available/

    # Enable influxdb.conf server
    install -d ${D}${sysconfdir}/nginx/sites-enabled
    ln -sf /etc/nginx/sites-available/influxdb.conf ${D}${sysconfdir}/nginx/sites-enabled/influxdb.conf

    # Install documentation
    install -d ${D}${docdir}/${PN}
    install -m 0644 ${WORKDIR}/README.md ${D}${docdir}/${PN}/
}

FILES:${PN} += " \
    ${sysconfdir}/influxdb/influxdb.conf \
    ${systemd_system_unitdir}/influxdb.service \
    ${sysconfdir}/nginx/sites-available/influxdb.conf \
    ${sysconfdir}/nginx/sites-enabled/influxdb.conf \
    ${docdir}/${PN}/README.md \
    /var/lib/influxdb \
    /var/log/influxdb \
"

SYSTEMD_SERVICE:${PN} = "influxdb.service"
SYSTEMD_AUTO_ENABLE:${PN} = "disable"

# Create i2r-system user/group for InfluxDB and nginx for web exposure
RDEPENDS:${PN} = "i2r-usersgroup nginx curl"

CVE_PRODUCT = "influxdb"
