[[inputs.cpu]]
  percpu = true
  totalcpu = true
  collect_cpu_time = false
  report_active = false
 
[[inputs.mem]]

[[inputs.chrony]]
  metrics = ["tracking"]

[[inputs.file]]
  files = ["/var/lib/i2r/metrics/modem-signal.txt"]
  data_format = "value"
  data_type = "integer"
  name_override = "modem_signal_level"
  interval = "15m"
 
[[outputs.graphite]]
  servers = ["localhost:2003"]
  prefix = "i2r"
  template = "host.tags.measurement.field"

[[outputs.influxdb_v2]]
  urls = ["http://localhost:8086"]
  token = "$INFLUXDB_TOKEN"
  organization = "i2r"
  bucket = "telegraf"
  timeout = "5s"
  user_agent = "telegraf"